import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hatomplayer/flutter_hatomplayer.dart';
import 'package:flutter_hatomplayer/hatom_player_event.dart';
import 'package:flutter_hatomplayer/play_config.dart';
import 'package:permission_handler/permission_handler.dart';

/// 海康威视视频播放页面
class VideoPlayerPage extends StatefulWidget {
  const VideoPlayerPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> {
  /// 播放地址 - 使用用户提供的RTSP地址
  static const String playUrl = 'rtsp://admin:wu215355@100.109.250.47:1555/Streaming/Channels/102';

  /// 播放控制器
  FlutterHatomplayer? player;

  /// 播放参数配置
  PlayConfig playConfig = PlayConfig(hardDecode: true, privateData: false);

  /// 视频尺寸
  Size? videoSize;

  /// 错误码
  String? errorCode;

  /// 播放状态
  bool isPlaying = false;

  /// 播放地址输入框控制器
  final TextEditingController playUrlController = TextEditingController(text: playUrl);

  @override
  void initState() {
    super.initState();
    // 请求权限
    _requestPermissions();
  }

  @override
  void dispose() {
    player?.stop();
    player?.release();
    super.dispose();
  }

  /// 请求必要权限
  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      await Permission.storage.request();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('海康威视播放器'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // 视频播放区域
          Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.width * 9 / 16,
            color: Colors.black,
            child: _buildVideoWidget(),
          ),
          // 控制区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'RTSP播放地址:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: playUrlController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: '请输入RTSP播放地址',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  // 播放控制按钮
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: isPlaying ? null : _startPlay,
                          child: const Text('开始播放'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: !isPlaying ? null : _stopPlay,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('停止播放'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // 状态信息
                  if (errorCode != null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        border: Border.all(color: Colors.red.shade200),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: Colors.red.shade600),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '播放失败: $errorCode',
                              style: TextStyle(color: Colors.red.shade700),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (isPlaying && errorCode == null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        border: Border.all(color: Colors.green.shade200),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.play_circle, color: Colors.green.shade600),
                          const SizedBox(width: 8),
                          Text(
                            '正在播放',
                            style: TextStyle(color: Colors.green.shade700),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建视频播放widget
  Widget _buildVideoWidget() {
    if (errorCode != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red.shade300),
            const SizedBox(height: 8),
            Text(
              '播放失败',
              style: TextStyle(color: Colors.red.shade300, fontSize: 16),
            ),
          ],
        ),
      );
    }

    /// 必须要等到视频尺寸回调才能去显示Texture
    if (videoSize != null && player?.textureId != null) {
      return Center(
        child: AspectRatio(
          aspectRatio: videoSize?.aspectRatio ?? 16 / 9,
          child: Texture(textureId: player?.textureId ?? -1),
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (isPlaying) ...[
            const CircularProgressIndicator(color: Colors.white),
            const SizedBox(height: 16),
            const Text(
              '正在连接...',
              style: TextStyle(color: Colors.white),
            ),
          ] else ...[
            Icon(Icons.videocam_off, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 8),
            Text(
              '点击开始播放',
              style: TextStyle(color: Colors.grey.shade400, fontSize: 16),
            ),
          ],
        ],
      ),
    );
  }

  /// 开始播放
  Future<void> _startPlay() async {
    try {
      setState(() {
        isPlaying = true;
        errorCode = null;
      });

      await _initPlayer();
      await player?.start();
    } catch (e) {
      setState(() {
        isPlaying = false;
        errorCode = e.toString();
      });
    }
  }

  /// 停止播放
  Future<void> _stopPlay() async {
    await player?.stop();
    setState(() {
      isPlaying = false;
      videoSize = null;
      errorCode = null;
    });
  }

  /// 初始化播放器
  Future<void> _initPlayer() async {
    Map<String, dynamic> headers = {'token': ''};
    
    player = FlutterHatomplayer(
      playConfig: playConfig,
      urlPath: playUrlController.text.trim(),
      headers: headers,
      playEventCallBack: (event) async {
        debugPrint('播放事件回调: ${event.event}');
        
        switch (event.event) {
          case EVENT_PLAY_SUCCESS:
            setState(() {
              errorCode = null;
              videoSize = const Size(16, 9); // 默认16:9比例
            });
            _showMessage('播放成功', isError: false);
            break;
            
          case EVENT_PLAY_ERROR:
            setState(() {
              errorCode = event.body ?? '未知错误';
              isPlaying = false;
            });
            _showMessage('播放失败: ${event.body}', isError: true);
            await player?.stop();
            break;
            
          case EVENT_PLAY_FINISH:
            setState(() {
              isPlaying = false;
              videoSize = null;
            });
            _showMessage('播放结束', isError: false);
            break;
            
          default:
            debugPrint('未处理的播放事件: ${event.event}');
        }
      },
    );

    bool result = await player?.initialize() ?? false;
    if (!result) {
      throw Exception('播放器初始化失败');
    }
  }

  /// 显示消息
  void _showMessage(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
